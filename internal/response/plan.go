package response

import "loop/pkg/types"

// UserQuestionnaireResp represents the response for a user's questionnaire
type UserQuestionnaireResp struct {
	Id                string `json:"id"`
	MotivationSource  string `json:"motivationSource"`
	DesiredAbility    string `json:"desiredAbility"`
	CurrentLevel      string `json:"currentLevel"`
	TargetLevel       string `json:"targetLevel"`
	DailyStudyMinutes int    `json:"dailyStudyMinutes"`
	CreatedAt         string `json:"createdAt"`
	UpdatedAt         string `json:"updatedAt"`
}

// LearningLevelResp represents a learning level
type LearningLevelResp struct {
	Id          uint   `json:"id"`
	Code        string `json:"code"`
	Name        string `json:"name"`
	Description string `json:"description"`
	ParentCode  string `json:"parentCode,omitempty"`
}

// PlanResourceResp represents a resource in a learning plan
type PlanResourceResp struct {
	ResourceId    string `json:"resourceId"`
	ResourceName  string `json:"resourceName"`
	ResourceCover string `json:"resourceCover,omitempty"`
	LsCount       int    `json:"lsCount"`
}

// PlanWeekResourceResp represents a resource in a weekly plan
type PlanWeekResourceResp struct {
	ResourceId    string `json:"resourceId"`
	ResourceName  string `json:"resourceName"`
	ResourceCover string `json:"resourceCover,omitempty"`
	LsCount       int    `json:"lsCount"`
}

// PlanDayResourceResp represents a resource in a day plan
type PlanDayResourceResp struct {
	ResourceId    string `json:"resourceId"`
	ResourceName  string `json:"resourceName"`
	ResourceCover string `json:"resourceCover"`
	ResourceUrl   string `json:"resourceUrl"`
}

// PlanDayResp represents a day in a learning plan week
type PlanDayResp struct {
	Id               string                `json:"id"`
	DayNumber        int                   `json:"dayNumber"`        // 1-7，代表周一到周日
	Status           int                   `json:"status"`           // 学习状态 0未开始 1进行中 2已完成
	CurrentSentences int                   `json:"currentSentences"` // 当前已学习句数
	TargetSentences  int                   `json:"targetSentences"`  // 目标学习句数
	StudyDate        string                `json:"studyDate"`        // 学习日期
	Resources        []PlanDayResourceResp `json:"resources"`        // 该天的资源详细信息
}

// PlanWeekResp represents a week in a learning plan stage
type PlanWeekResp struct {
	WeekNumber int           `json:"weekNumber"`
	Days       []PlanDayResp `json:"days"` // 该周的学习天数据
}

// PlanStageResp represents a stage in a learning plan
type PlanStageResp struct {
	Id        string         `json:"id"`
	StageDesc string         `json:"stageDesc"` // 阶段描述，如 A0-1, A1-2 等
	Objective string         `json:"objective"`
	Weeks     []PlanWeekResp `json:"weeks,omitempty"` // 按周划分的学习计划
}

// LearningPlanResp represents a learning plan
type LearningPlanResp struct {
	Id              string          `json:"id"`
	StartLevel      string          `json:"startLevel"`
	TargetLevel     string          `json:"targetLevel"`
	StartDate       string          `json:"startDate"`
	EndDate         string          `json:"endDate,omitempty"`
	Status          int             `json:"status"`
	StudyDaysOfWeek *types.IntArray `json:"studyDaysOfWeek"`
	DailySentences  int             `json:"dailySentences"`
	CurrentDayId    string          `json:"currentDayId,omitempty"` // 当前应该学习的天ID
	Stages          []PlanStageResp `json:"stages"`
	CreatedAt       string          `json:"createdAt"`
}

// PlanSummaryResp represents a summary of a learning plan
type PlanSummaryResp struct {
	Id          string `json:"id"`
	StartLevel  string `json:"startLevel"`
	TargetLevel string `json:"targetLevel"`
	StartDate   string `json:"startDate"`
	EndDate     string `json:"endDate,omitempty"`
	Status      int    `json:"status"`
	StageCount  int    `json:"stageCount"`
	CreatedAt   string `json:"createdAt"`
}

// LearningWeekResp 表示学习周计划的响应
type LearningWeekResp struct {
	Id         string            `json:"id"`
	WeekNumber int               `json:"weekNumber"`
	StartDate  string            `json:"startDate"`
	EndDate    string            `json:"endDate"`
	Days       []LearningDayResp `json:"days"`
}

// LearningDayResp 表示学习日计划的响应
type LearningDayResp struct {
	Id          string   `json:"id"`
	DayNumber   int      `json:"dayNumber"`
	ResourceIds []string `json:"resourceIds"`
}
