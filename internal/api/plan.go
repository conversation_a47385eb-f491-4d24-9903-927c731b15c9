package api

import (
	"loop/internal/data"
	"loop/internal/request"

	"github.com/gin-gonic/gin"
)

func NewPlanApi(repo *data.PlanRepo) *PlanApi {
	return &PlanApi{repo: repo}
}

type PlanApi struct {
	Apis
	repo *data.PlanRepo
}

// GetQuestionnaire 获取用户的问卷信息
// @Summary 获取用户问卷
// @Description 获取用户的学习问卷信息
// @Tags 学习计划
// @Accept json
// @Produce json
// @Success 200 {object} web.JsonResult{data=response.UserQuestionnaireResp}
// @Router /api/v1/plan/questionnaire [get]
func (a *PlanApi) GetQuestionnaire(c *gin.Context) {
	a.processRequest(c, nil, func(r any) any {
		return a.repo.GetQuestionnaire(c)
	})
}

// GeneratePlan 为用户生成学习计划
// @Summary 生成学习计划
// @Description 基于用户问卷生成学习计划
// @Tags 学习计划
// @Accept json
// @Produce json
// @Param request body request.GeneratePlanReq false "生成计划参数"
// @Success 200 {object} web.JsonResult{data=response.LearningPlanResp}
// @Router /api/v1/plan/generate [post]
func (a *PlanApi) GeneratePlan(c *gin.Context) {
	var req request.GeneratePlanReq
	a.processRequest(c, &req, func(r any) any {
		reqTyped := r.(*request.GeneratePlanReq)
		return a.repo.GeneratePlan(c, *reqTyped)
	})
}

// GetPlan 获取用户当前活跃的学习计划
// @Summary 获取学习计划
// @Description 获取用户当前活跃的学习计划
// @Tags 学习计划
// @Accept json
// @Produce json
// @Param planId query string false "计划ID，如果不提供则获取用户当前活跃的计划"
// @Success 200 {object} web.JsonResult{data=response.LearningPlanResp}
// @Router /api/v1/plan [get]
func (a *PlanApi) GetPlan(c *gin.Context) {
	var req request.GetPlanReq
	a.processRequest(c, &req, func(r any) any {
		reqTyped := r.(*request.GetPlanReq)
		return a.repo.GetPlan(c, *reqTyped)
	})
}

// InvalidatePlan 使用户的学习计划失效
// @Summary 使学习计划失效
// @Description 使用户的学习计划失效
// @Tags 学习计划
// @Accept json
// @Produce json
// @Param request body request.InvalidatePlanReq false "使计划失效参数"
// @Success 200 {object} web.JsonResult
// @Router /api/v1/plan/invalidate [post]
func (a *PlanApi) InvalidatePlan(c *gin.Context) {
	var req request.InvalidatePlanReq
	a.processRequest(c, &req, func(r any) any {
		reqTyped := r.(*request.InvalidatePlanReq)
		return a.repo.InvalidatePlan(c, *reqTyped)
	})
}

// GenerateFixedPlan 生成固定的4周学习计划
// @Summary 生成固定学习计划
// @Description 生成固定的4周学习计划，使用精选资源的第一个或资源库的第一个
// @Tags 学习计划
// @Accept json
// @Produce json
// @Param request body request.GenerateFixedPlanReq false "生成固定计划参数"
// @Success 200 {object} web.JsonResult
// @Router /api/v1/plan/generateFixed [post]
func (a *PlanApi) GenerateFixedPlan(c *gin.Context) {
	var req request.GenerateFixedPlanReq
	a.processRequest(c, &req, func(r any) any {
		reqTyped := r.(*request.GenerateFixedPlanReq)
		return a.repo.GenerateFixedPlan(c, *reqTyped)
	})
}

// UpdateSentences 更新学习句数
// @Summary 更新学习句数
// @Description 更新指定日期的学习句数，自动更新学习状态
// @Tags 学习计划
// @Accept json
// @Produce json
// @Param request body request.UpdateSentencesReq true "更新句数参数"
// @Success 200 {object} web.JsonResult
// @Router /api/v1/plan/updateSentences [post]
func (a *PlanApi) UpdateSentences(c *gin.Context) {
	var req request.UpdateSentencesReq
	a.processRequest(c, &req, func(r any) any {
		reqTyped := r.(*request.UpdateSentencesReq)
		return a.repo.UpdateSentences(c, *reqTyped)
	})
}

// UpdateStudyDays 修改学习计划的学习日期配置
// @Summary 修改学习日期
// @Description 修改学习计划的学习日期配置，已过去的学习天不会被修改
// @Tags 学习计划
// @Accept json
// @Produce json
// @Param request body request.UpdateStudyDaysReq true "修改学习日期参数"
// @Success 200 {object} web.JsonResult
// @Router /api/v1/plan/updateStudyDays [post]
func (a *PlanApi) UpdateStudyDays(c *gin.Context) {
	var req request.UpdateStudyDaysReq
	a.processRequest(c, &req, func(r any) any {
		reqTyped := r.(*request.UpdateStudyDaysReq)
		return a.repo.UpdateStudyDays(c, *reqTyped)
	})
}

// GetPlanWithWeeksAndDays 获取包含周和日数据的完整学习计划
// @Summary 获取完整学习计划
// @Description 获取包含周和日数据的完整学习计划
// @Tags 学习计划
// @Accept json
// @Produce json
// @Param planId query string false "计划ID，如果不提供则获取用户当前活跃的计划"
// @Success 200 {object} web.JsonResult{data=response.LearningPlanResp}
// @Router /api/v1/plan/detail [get]
func (a *PlanApi) GetPlanWithWeeksAndDays(c *gin.Context) {
	var req request.GetPlanReq
	a.processRequest(c, &req, func(r any) any {
		reqTyped := r.(*request.GetPlanReq)
		return a.repo.GetPlanWithWeeksAndDays(c, *reqTyped)
	})
}
