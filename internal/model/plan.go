package model

import (
	"fmt"
	"loop/internal/config"
	"loop/pkg/dbx"
	"loop/pkg/enum"
	"loop/pkg/types"
	"time"

	"go4.org/syncutil/singleflight"
)

// UserQuestionnaire 存储用户的问卷回答
type UserQuestionnaire struct {
	Model
	Uid               string `gorm:"not null;index:idx_user_questionnaire,priority:1"`
	MotivationSource  string `gorm:"type:text;comment:用户的动力来源"`
	DesiredAbility    string `gorm:"type:text;comment:用户想提高的能力"`
	CurrentLevel      string `gorm:"size:10;comment:用户当前级别"`
	TargetLevel       string `gorm:"size:10;comment:用户目标级别"`
	DailyStudyMinutes int    `gorm:"type:int;comment:用户每天学习时间(分钟)"`
}

func (UserQuestionnaire) TableName() string {
	return "user_questionnaires"
}

// LearningPlan 表示用户的学习计划
type LearningPlan struct {
	Model
	Uid             string    `gorm:"not null;index:idx_user_plan,priority:1"`
	StartLevel      string    `gorm:"size:10;not null;comment:起始级别"`
	TargetLevel     string    `gorm:"size:10;not null;comment:目标级别"`
	StartDate       time.Time `gorm:"not null;comment:计划开始日期"`
	EndDate         time.Time `gorm:"comment:计划结束日期"`
	Status          int       `gorm:"type:tinyint;default:1;comment:状态 1进行中 0已作废"`
	QuestionnaireId string    `gorm:"size:20;comment:关联的问卷ID"`
	// 学习计划配置
	StudyDaysOfWeek types.IntArray `gorm:"type:json;comment:每周学习的具体日期，1-7代表周一到周日，如[1,2,3,4]表示周一到周四学习"`
	DailySentences  int            `gorm:"type:int;default:10;comment:每天学习句数目标"`
}

func (LearningPlan) TableName() string {
	return "learning_plans"
}

// LearningPlanStage 表示学习计划中的一个阶段
type LearningPlanStage struct {
	Model
	PlanId      string `gorm:"not null;index:idx_plan_stage,priority:1;comment:关联的计划ID"`
	Uid         string `gorm:"not null;index:idx_user_stage,priority:1;comment:用户ID"`
	StageDesc   string `gorm:"size:20;comment:阶段描述，如 A0-1, A1-2 等"`
	Objective   string `gorm:"type:text;comment:阶段目标"`
	SortOrder   int    `gorm:"type:int;default:0;comment:排序顺序"`
	ResourceIds string `gorm:"type:text;comment:资源ID列表，JSON格式"`
}

func (LearningPlanStage) TableName() string {
	return "learning_plan_stages"
}

// LearningPlanWeek 表示学习计划中的一周
type LearningPlanWeek struct {
	Model
	StageId         string         `gorm:"not null;index:idx_stage_week,priority:1;comment:关联的阶段ID"`
	PlanId          string         `gorm:"not null;index:idx_plan_week,priority:1;comment:关联的计划ID"`
	Uid             string         `gorm:"not null;index:idx_user_week,priority:1;comment:用户ID"`
	WeekNumber      int            `gorm:"type:int;not null;comment:周数，从1开始"`
	StudyDaysOfWeek types.IntArray `gorm:"type:json;comment:每周学习的具体日期，1-7代表周一到周日，如[1,2,3,4]表示周一到周四学习"`
	StartDate       time.Time      `gorm:"not null;comment:周开始日期"`
	EndDate         time.Time      `gorm:"not null;comment:周结束日期"`
}

func (LearningPlanWeek) TableName() string {
	return "learning_plan_weeks"
}

// LearningPlanDay 表示学习计划中的一天
type LearningPlanDay struct {
	Model
	WeekId           string    `gorm:"not null;index:idx_week_day,priority:1;comment:关联的周ID"`
	StageId          string    `gorm:"not null;index:idx_stage_day,priority:1;comment:关联的阶段ID"`
	PlanId           string    `gorm:"not null;index:idx_plan_day,priority:1;comment:关联的计划ID"`
	Uid              string    `gorm:"not null;index:idx_user_day,priority:1;comment:用户ID"`
	DayNumber        int       `gorm:"type:int;not null;comment:天数，从1开始（周一到周日）"`
	ResourceIds      string    `gorm:"type:text;comment:资源ID列表，JSON格式"`
	StudyDate        time.Time `gorm:"not null;comment:实际学习日期"`
	Status           int       `gorm:"type:tinyint;default:0;comment:学习状态 0未开始 1进行中 2已完成"`
	CurrentSentences int       `gorm:"type:int;default:0;comment:当前已学习句数"`
	TargetSentences  int       `gorm:"type:int;default:0;comment:目标学习句数"`
}

func (LearningPlanDay) TableName() string {
	return "learning_plan_days"
}

func NewPlanModel(dbModel *DbModel, config *config.Config) *PlanModel {
	return &PlanModel{
		DbModel: dbModel,
		config:  config,
		sg:      &singleflight.Group{},
	}
}

type PlanModel struct {
	*DbModel
	config *config.Config
	sg     *singleflight.Group
}

// GetUserQuestionnaire 获取用户的问卷信息
func (p *PlanModel) GetUserQuestionnaire(uid string) (*UserQuestionnaire, error) {
	res, err := p.sg.Do(fmt.Sprintf("get_user_questionnaire_%s", uid), func() (any, error) {
		query := UserQuestionnaire{Uid: uid}
		questionnaire := UserQuestionnaire{}
		found, err := p.GetOne(&questionnaire, query)
		if err != nil {
			return nil, err
		}
		if !found {
			return nil, nil
		}
		return &questionnaire, nil
	})
	if err != nil {
		return nil, err
	}
	if res == nil {
		return nil, nil
	}
	data, ok := res.(*UserQuestionnaire)
	if !ok {
		return nil, fmt.Errorf("type assertion to *UserQuestionnaire failed")
	}
	return data, nil
}

// GetActiveLearningPlan 获取用户当前活跃的学习计划
func (p *PlanModel) GetActiveLearningPlan(uid string) (*LearningPlan, error) {
	res, err := p.sg.Do(fmt.Sprintf("get_active_learning_plan_%s", uid), func() (any, error) {
		query := LearningPlan{Uid: uid, Status: 1}
		plan := LearningPlan{}
		found, err := p.GetOne(&plan, query)
		if err != nil {
			return nil, err
		}
		if !found {
			return nil, nil
		}
		return &plan, nil
	})
	if err != nil {
		return nil, err
	}
	if res == nil {
		return nil, nil
	}
	data, ok := res.(*LearningPlan)
	if !ok {
		return nil, fmt.Errorf("type assertion to *LearningPlan failed")
	}
	return data, nil
}

// GetPlanStages 获取学习计划的所有阶段
func (p *PlanModel) GetPlanStages(planId string) ([]*LearningPlanStage, error) {
	var stages []*LearningPlanStage
	err := p.GetList(&stages, "plan_id = ? ORDER BY sort_order ASC", planId)
	if err != nil {
		return nil, err
	}
	return stages, nil
}

// GetUserPlanStages 获取用户学习计划的所有阶段
func (p *PlanModel) GetUserPlanStages(uid string, planId string) ([]*LearningPlanStage, error) {
	var stages []*LearningPlanStage
	err := p.GetList(&stages, "uid = ? AND plan_id = ? ORDER BY sort_order ASC", uid, planId)
	if err != nil {
		return nil, err
	}
	return stages, nil
}

// InvalidateUserPlans 将用户所有现有计划标记为无效
func (p *PlanModel) InvalidateUserPlans(uid string) error {
	return p.Tx(func(txDb *dbx.DBExtension) error {
		if err := txDb.Model(&LearningPlan{}).Where("uid = ? AND status = ?", uid, 1).Update("status", 0).Error; err != nil {
			return err
		}
		return nil
	})
}

// GetFirstFeaturedResource 获取第一个精选资源，如果没有则获取第一个普通资源
func (p *PlanModel) GetFirstFeaturedResource(targetLangCode string) (*Resource, error) {
	// 首先尝试获取精选资源
	var featuredContents []*FeaturedContent
	err := p.GetList(&featuredContents, "content_type = ? AND lang_code = ?", int(enum.Resource), targetLangCode)
	if err != nil {
		return nil, err
	}

	// 如果有精选资源，返回第一个
	if len(featuredContents) > 0 {
		resource := &Resource{}
		found, err := p.GetOne(resource, Resource{Model: Model{Id: featuredContents[0].ContentID}})
		if err != nil {
			return nil, err
		}
		if found {
			return resource, nil
		}
	}

	// 如果没有精选资源，获取第一个普通资源
	var resources []*Resource
	err = p.GetList(&resources, "")
	if err != nil {
		return nil, err
	}

	if len(resources) > 0 {
		return resources[0], nil
	}

	return nil, fmt.Errorf("no resources found")
}

// GetPlanWeeks 获取学习计划阶段的所有周
func (p *PlanModel) GetPlanWeeks(stageId string) ([]*LearningPlanWeek, error) {
	var weeks []*LearningPlanWeek
	err := p.GetList(&weeks, "stage_id = ? ORDER BY week_number ASC", stageId)
	if err != nil {
		return nil, err
	}
	return weeks, nil
}

// GetPlanAllWeeks 获取学习计划的所有周（直接通过planId查询）
func (p *PlanModel) GetPlanAllWeeks(planId string) ([]*LearningPlanWeek, error) {
	var weeks []*LearningPlanWeek
	err := p.GetList(&weeks, "plan_id = ? ORDER BY week_number ASC", planId)
	if err != nil {
		return nil, err
	}
	return weeks, nil
}

// GetUserPlanWeeks 获取用户学习计划的所有周
func (p *PlanModel) GetUserPlanWeeks(uid string, planId string) ([]*LearningPlanWeek, error) {
	var weeks []*LearningPlanWeek
	err := p.GetList(&weeks, "uid = ? AND plan_id = ? ORDER BY week_number ASC", uid, planId)
	if err != nil {
		return nil, err
	}
	return weeks, nil
}

// GetWeekDays 获取学习计划周的所有天
func (p *PlanModel) GetWeekDays(weekId string) ([]*LearningPlanDay, error) {
	var days []*LearningPlanDay
	err := p.GetList(&days, "week_id = ? ORDER BY day_number ASC", weekId)
	if err != nil {
		return nil, err
	}
	return days, nil
}

// GetUserPlanDays 获取用户学习计划的所有天
func (p *PlanModel) GetUserPlanDays(uid string, planId string) ([]*LearningPlanDay, error) {
	var days []*LearningPlanDay
	err := p.GetList(&days, "uid = ? AND plan_id = ? ORDER BY study_date ASC", uid, planId)
	if err != nil {
		return nil, err
	}
	return days, nil
}

// GetUserPlanDaysByDateRange 根据日期范围获取用户学习计划的天数据
func (p *PlanModel) GetUserPlanDaysByDateRange(uid string, planId string, startDate, endDate time.Time) ([]*LearningPlanDay, error) {
	var days []*LearningPlanDay
	err := p.GetList(&days, "uid = ? AND plan_id = ? AND study_date >= ? AND study_date <= ? ORDER BY study_date ASC",
		uid, planId, startDate, endDate)
	if err != nil {
		return nil, err
	}
	return days, nil
}

// GetWeeksDays 批量获取多个周的所有天数据
func (p *PlanModel) GetWeeksDays(weekIds []string) ([]*LearningPlanDay, error) {
	if len(weekIds) == 0 {
		return []*LearningPlanDay{}, nil
	}

	var days []*LearningPlanDay
	err := p.GetList(&days, "week_id IN (?) ORDER BY week_id, day_number ASC", weekIds)
	if err != nil {
		return nil, err
	}
	return days, nil
}

// UpdateDaySentences 更新学习天的句数并自动更新状态
func (p *PlanModel) UpdateDaySentences(dayId string, sentences int) error {
	return p.Tx(func(txDb *dbx.DBExtension) error {
		// 获取学习天数据
		day := &LearningPlanDay{Model: Model{Id: dayId}}
		found, err := p.GetOne(day, *day)
		if err != nil {
			return err
		}
		if !found {
			return fmt.Errorf("learning plan day not found: %s", dayId)
		}

		// 更新当前句数
		day.CurrentSentences = sentences

		// 自动更新状态
		if sentences == 0 {
			day.Status = 0 // 未开始
		} else if sentences >= day.TargetSentences {
			day.Status = 2 // 已完成
		} else {
			day.Status = 1 // 进行中
		}

		// 保存更新
		return txDb.Save(day).Error
	})
}

// GetDayByDate 根据用户ID和日期获取学习天数据
func (p *PlanModel) GetDayByDate(uid string, date time.Time) (*LearningPlanDay, error) {
	// 获取用户活跃计划
	plan, err := p.GetActiveLearningPlan(uid)
	if err != nil {
		return nil, err
	}
	if plan == nil {
		return nil, fmt.Errorf("no active learning plan found for user: %s", uid)
	}

	// 直接通过用户ID、计划ID和日期查询
	day := &LearningPlanDay{}
	found, err := p.GetOne(day, LearningPlanDay{
		Uid:       uid,
		PlanId:    plan.Id,
		StudyDate: date,
	})
	if err != nil {
		return nil, err
	}
	if !found {
		return nil, fmt.Errorf("no learning day found for date: %s", date.Format("2006-01-02"))
	}
	return day, nil
}

// UpdateDaySentencesByDate 根据日期更新学习句数
func (p *PlanModel) UpdateDaySentencesByDate(uid string, date time.Time, sentences int) error {
	day, err := p.GetDayByDate(uid, date)
	if err != nil {
		return err
	}

	return p.UpdateDaySentences(day.Id, sentences)
}

// UpdateLearningPlanStudyDays 更新学习计划的学习日期配置
func (p *PlanModel) UpdateLearningPlanStudyDays(planId string, studyDaysOfWeek []int) error {
	return p.Tx(func(txDb *dbx.DBExtension) error {
		// 获取计划信息
		plan := &LearningPlan{Model: Model{Id: planId}}
		found, err := p.GetOne(plan, *plan)
		if err != nil {
			return err
		}
		if !found {
			return fmt.Errorf("learning plan not found: %s", planId)
		}

		// 更新计划的学习日期配置
		plan.StudyDaysOfWeek = studyDaysOfWeek
		if err := txDb.Save(plan).Error; err != nil {
			return err
		}

		// 获取今天的时间
		today := time.Now()
		todayStart := time.Date(today.Year(), today.Month(), today.Day(), 0, 0, 0, 0, today.Location())

		// 删除今天及以后的学习天数据
		if err := txDb.Where("plan_id = ? AND study_date >= ?", planId, todayStart).Delete(&LearningPlanDay{}).Error; err != nil {
			return err
		}

		// 重新生成今天及以后的学习天数据
		return p.regenerateFutureLearningDays(txDb, plan, studyDaysOfWeek, todayStart)
	})
}

// regenerateFutureLearningDays 重新生成今天及以后的学习天数据
func (p *PlanModel) regenerateFutureLearningDays(txDb *dbx.DBExtension, plan *LearningPlan, studyDaysOfWeek []int, startDate time.Time) error {
	// 生成未来12周的数据
	for weekNum := 1; weekNum <= 12; weekNum++ {
		weekStartDate := startDate.AddDate(0, 0, (weekNum-1)*7)

		// 为每周的学习日生成天数据
		for dayIndex, dayOfWeek := range studyDaysOfWeek {
			// 计算实际的学习日期
			studyDate := weekStartDate
			for i := 1; i < dayOfWeek; i++ {
				studyDate = studyDate.AddDate(0, 0, 1)
			}

			// 只生成今天及以后的学习天
			if studyDate.Before(startDate) {
				continue
			}

			day := &LearningPlanDay{
				PlanId:           plan.Id,
				Uid:              plan.Uid,
				DayNumber:        dayIndex + 1,
				StudyDate:        studyDate,
				Status:           0, // 未开始
				CurrentSentences: 0,
				TargetSentences:  plan.DailySentences,
			}

			if err := txDb.Create(day).Error; err != nil {
				return err
			}
		}
	}

	return nil
}

// GetPlanAllDays 获取计划的所有天数据（按时间排序）
func (p *PlanModel) GetPlanAllDays(planId string) ([]*LearningPlanDay, error) {
	var days []*LearningPlanDay
	err := p.GetList(&days, "plan_id = ? ORDER BY study_date ASC", planId)
	if err != nil {
		return nil, err
	}
	return days, nil
}

// DeleteWeekDays 删除指定周的所有天数据
func (p *PlanModel) DeleteWeekDays(weekId string) error {
	return p.Tx(func(txDb *dbx.DBExtension) error {
		return txDb.Where("week_id = ?", weekId).Delete(&LearningPlanDay{}).Error
	})
}

// DeleteWeek 删除指定的周数据
func (p *PlanModel) DeleteWeek(weekId string) error {
	return p.Tx(func(txDb *dbx.DBExtension) error {
		// 先删除周下的所有天数据
		if err := p.DeleteWeekDays(weekId); err != nil {
			return err
		}
		// 再删除周数据
		return txDb.Where("id = ?", weekId).Delete(&LearningPlanWeek{}).Error
	})
}

// UpdateWeekStudyDays 更新周的学习天数配置
func (p *PlanModel) UpdateWeekStudyDays(weekId string, studyDaysOfWeek []int) error {
	return p.Tx(func(txDb *dbx.DBExtension) error {
		// 获取周信息
		week := &LearningPlanWeek{Model: Model{Id: weekId}}
		found, err := p.GetOne(week, *week)
		if err != nil {
			return err
		}
		if !found {
			return fmt.Errorf("learning plan week not found: %s", weekId)
		}

		// 更新周的学习日期配置
		week.StudyDaysOfWeek = studyDaysOfWeek
		if err := txDb.Save(week).Error; err != nil {
			return err
		}

		// 获取今天的时间
		today := time.Now()
		todayStart := time.Date(today.Year(), today.Month(), today.Day(), 0, 0, 0, 0, today.Location())

		// 删除今天及以后的学习天数据
		if err := txDb.Where("week_id = ? AND study_date >= ?", weekId, todayStart).Delete(&LearningPlanDay{}).Error; err != nil {
			return err
		}

		// 重新生成今天及以后的学习天数据
		return p.regenerateWeekLearningDays(txDb, week, todayStart)
	})
}

// regenerateWeekLearningDays 重新生成周内今天及以后的学习天数据
func (p *PlanModel) regenerateWeekLearningDays(txDb *dbx.DBExtension, week *LearningPlanWeek, startDate time.Time) error {
	// 为每周的学习日生成天数据
	for dayIndex, dayOfWeek := range week.StudyDaysOfWeek {
		// 计算实际的学习日期
		studyDate := week.StartDate
		for i := 1; i < dayOfWeek; i++ {
			studyDate = studyDate.AddDate(0, 0, 1)
		}

		// 只生成今天及以后的学习天
		if studyDate.Before(startDate) {
			continue
		}

		day := &LearningPlanDay{
			WeekId:           week.Id,
			PlanId:           week.PlanId,
			Uid:              week.Uid,
			DayNumber:        dayIndex + 1,
			StudyDate:        studyDate,
			Status:           0, // 未开始
			CurrentSentences: 0,
			TargetSentences:  10, // 默认值，实际应该从计划中获取
		}

		if err := txDb.Create(day).Error; err != nil {
			return err
		}
	}

	return nil
}
