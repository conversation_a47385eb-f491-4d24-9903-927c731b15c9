package request

import "loop/pkg/types"

type VideoDetailReq struct {
	LocalVideoPath string `form:"localVideoPath" json:"localVideoPath"` //只有本地视频的时候才传
	FileName       string `form:"fileName" json:"fileName"`             //只有本地视频的时候才传
	ResourceId     string `form:"resourceId" json:"resourceId"`         //远程视频的资源ID
	ResourceType   int    `form:"resourceType" json:"resourceType" binding:"required"`
}

type SubtitleUpdateReq struct {
	ResourceId   string                       `form:"resourceId" json:"resourceId"`
	ResourceType int                          `form:"resourceType" json:"resourceType" binding:"required"`
	SubtitleUrl  string                       `form:"subtitleUrl" json:"subtitleUrl"`
	Skips        types.VideoTimeIntervalArray `form:"skips" json:"skips"`
	ForceSkips   bool                         `form:"forceSkips" json:"forceSkips"` //是否强制更新Skips，如果是就直接更新skip为传递来的
}
type NotesAddReq struct {
	Id             string `form:"id" json:"id"`
	ResourceId     string `form:"resourceId" json:"resourceId" `
	ResourceType   int    `form:"resourceType" json:"resourceType" binding:"required"`
	Content        string `form:"content" json:"content" binding:"required"`
	VideoStartTime int64  `form:"videoStartTime" json:"videoStartTime" binding:"required"`
	VideoEndTime   int64  `form:"videoEndTime" json:"videoEndTime" binding:"required"`
}
type VideoDetailUpdateReq struct {
	ResourceId   string `form:"resourceId" json:"resourceId" binding:"required"`
	ResourceType int    `form:"resourceType" json:"resourceType" binding:"required"`
	Position     int64  `form:"position" json:"position" binding:"intCanBeZero"`
}
type NotesReq struct {
	CurrentPage  int    `form:"currentpage" json:"currentpage" `
	PageSize     int    `form:"pagesize" json:"pagesize" `
	ResourceId   string `form:"resourceId" json:"resourceId" binding:"required"`
	ResourceType int    `form:"resourceType" json:"resourceType" binding:"required"`
}
type SentenceCollectReq struct {
	SubtitleId string `form:"subtitleId" json:"subtitleId" binding:"required"`
}
type AiSentenceReq struct {
	Sentence string `form:"sentence" json:"sentence" binding:"required"`
}
type SentenceCollectAddReq struct {
	ResourceId   string                       `form:"resourceId" json:"resourceId" binding:"required"`
	ResourceType int                          `form:"resourceType" json:"resourceType" binding:"required"`
	Times        types.VideoTimeIntervalArray `form:"times" json:"times"` //可以多个或者一个
}

// 更新本地视频资源所有字段（全量覆盖）
type UserLocalResourceUpdateReq struct {
	ResourceId      string             `form:"resourceId" json:"resourceId" binding:"required"`
	VideoUrl        *string            `form:"videoUrl" json:"videoUrl"`
	FileName        *string            `form:"fileName" json:"fileName"`
	Position        *int64             `form:"position" json:"position"`
	LocalVideoPaths *types.StringArray `form:"localVideoPaths" json:"localVideoPaths"`
}
