package util

import (
	"fmt"
	"loop/internal/model"
	"loop/internal/request"
	"time"
)

// ProcessLearningSessionData 处理学习会话数据，转换为DataEpisodeEach模型
func ProcessLearningSessionData(uid string, req request.DataEpisodeEachReq) (*model.DataEpisodeEach, error) {
	sessionData := req.SessionData

	episodeEach := &model.DataEpisodeEach{
		Uid:                  uid,
		ResourceId:           sessionData.ResourceId,
		ResourceType:         sessionData.ResourceType,
		CurrentLsTimes:       sessionData.LsTimes,
		LearnDuration:        *sessionData.TotalSessionDuration,
		SessionStartTime:     sessionData.SessionStartTime,
		SessionEndTime:       sessionData.SessionEndTime,
		TotalPlayDuration:    *sessionData.TotalPlayDuration,
		TotalRecordDuration:  *sessionData.TotalRecordDuration,
		TotalSessionDuration: *sessionData.TotalSessionDuration,
		Sentences:            req.SentenceRecords,
	}

	return episodeEach, nil
}

// ValidateSessionData 验证会话数据的有效性
func ValidateSessionData(sessionData request.LearningSessionReq) error {
	if sessionData.ResourceId == "" {
		return fmt.Errorf("资源ID不能为空")
	}

	if sessionData.ResourceType <= 0 {
		return fmt.Errorf("资源类型无效")
	}

	if sessionData.SessionStartTime <= 0 || sessionData.SessionEndTime <= 0 {
		return fmt.Errorf("会话时间无效")
	}

	if sessionData.SessionEndTime <= sessionData.SessionStartTime {
		return fmt.Errorf("结束时间必须大于开始时间")
	}

	if sessionData.LsTimes <= 0 {
		return fmt.Errorf("LS次数必须大于0")
	}

	return nil
}

// CalculateTotalDuration 计算总学习时长的通用方法
func CalculateTotalDuration(episodes []*model.DataEpisodeEach) int64 {
	var totalDuration int64
	for _, episode := range episodes {
		totalDuration += episode.LearnDuration
	}
	return totalDuration
}

// TimeRange 时间范围结构
type TimeRange struct {
	Start time.Time
	End   time.Time
}

// GetTimeRange 根据类型获取时间范围
func GetTimeRange(timeType int, startTime, endTime int64) TimeRange {
	switch timeType {
	case 1: // 日
		return getDayRange(startTime, endTime)
	case 2: // 周
		return getWeekRange(startTime, endTime)
	case 3: // 月
		return getMonthRange(startTime, endTime)
	case 4: // 年
		return getYearRange(startTime, endTime)
	default:
		now := time.Now()
		return TimeRange{Start: now, End: now}
	}
}

func getDayRange(startTime, endTime int64) TimeRange {
	var start, end time.Time
	if startTime != 0 {
		start = time.Unix(startTime, 0)
	} else {
		now := time.Now()
		start = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	}
	if endTime != 0 {
		end = time.Unix(endTime, 0)
	} else {
		end = start.AddDate(0, 0, 1)
	}
	return TimeRange{Start: start, End: end}
}

func getWeekRange(startTime, endTime int64) TimeRange {
	var start, end time.Time
	if startTime != 0 {
		start = time.Unix(startTime, 0)
	} else {
		now := time.Now()
		start = now.AddDate(0, 0, -int(now.Weekday())+1).Truncate(24 * time.Hour)
	}
	if endTime != 0 {
		end = time.Unix(endTime, 0)
	} else {
		end = start.AddDate(0, 0, 7)
	}
	return TimeRange{Start: start, End: end}
}

func getMonthRange(startTime, endTime int64) TimeRange {
	var start, end time.Time
	if startTime != 0 {
		start = time.Unix(startTime, 0)
	} else {
		start = time.Now()
	}
	if endTime != 0 {
		end = time.Unix(endTime, 0)
	} else {
		end = start.AddDate(0, 1, 0)
	}
	return TimeRange{Start: start, End: end}
}

func getYearRange(startTime, endTime int64) TimeRange {
	var start, end time.Time
	if startTime != 0 {
		start = time.Unix(startTime, 0)
	} else {
		start = time.Now()
	}
	if endTime != 0 {
		end = time.Unix(endTime, 0)
	} else {
		end = time.Now()
	}
	return TimeRange{Start: start, End: end}
}

// FormatDateString 格式化日期字符串
func FormatDateString(t time.Time, isDayData bool) string {
	if isDayData {
		return fmt.Sprintf("%d月%d日 %02d-%02d", t.Month(), t.Day(), t.Hour(), t.Hour()+1)
	}
	return fmt.Sprintf("%d年%d月%d日", t.Year(), t.Month(), t.Day())
}
