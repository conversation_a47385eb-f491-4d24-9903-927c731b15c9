package data

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"loop/internal/client"
	"loop/internal/config"
	"loop/internal/data/types"
	"loop/internal/data/util"
	"loop/internal/model"
	"loop/internal/request"
	"loop/internal/response"
	"loop/pkg/dbx"
	"loop/pkg/jwtx"
	"loop/pkg/oss"
	"loop/pkg/timex"
	"loop/pkg/web"
	"os"
	"sort"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"github.com/sirupsen/logrus"
	"go4.org/syncutil/singleflight"
)

func NewPlanRepo(
	model *model.PlanModel,
	resourceModel *model.ResourceModel,
	config *config.Config,
	client *client.Client,
) *PlanRepo {
	return &PlanRepo{
		model:         model,
		resourceModel: resourceModel,
		config:        config,
		client:        client,
	}
}

type PlanRepo struct {
	model         *model.PlanModel
	resourceModel *model.ResourceModel
	config        *config.Config
	client        *client.Client
}

// GetQuestionnaire 获取用户的问卷信息
func (r *PlanRepo) GetQuestionnaire(c *gin.Context) *web.JsonResult {
	uid := jwtx.GetUid(c)

	questionnaire, err := r.model.GetUserQuestionnaire(uid)
	if err != nil {
		logrus.WithError(err).Error("Failed to get user questionnaire")
		return web.JsonInternalError(err)
	}

	if questionnaire == nil {
		return web.JsonEntityNotFound(c)
	}

	resp := &response.UserQuestionnaireResp{}
	copier.Copy(resp, questionnaire)
	resp.CreatedAt = timex.FormatRFC3339(questionnaire.CreatedAt)
	resp.UpdatedAt = timex.FormatRFC3339(questionnaire.UpdatedAt)

	return web.JsonData(resp)
}

// GeneratePlan 为用户生成学习计划
// 添加 singleflight 组
var generatePlanGroup singleflight.Group

// 全局状态映射，用于跟踪计划生成状态
var planGenerationStatusMap = sync.Map{}

// 检查是否有正在进行的计划生成
func (r *PlanRepo) isGeneratingPlan(uid string) bool {
	_, ok := planGenerationStatusMap.Load(uid)
	return ok
}

// 标记计划生成开始
func (r *PlanRepo) markPlanGenerationStarted(uid string) {
	planGenerationStatusMap.Store(uid, true)
}

// 标记计划生成结束
func (r *PlanRepo) markPlanGenerationCompleted(uid string) {
	planGenerationStatusMap.Delete(uid)
}

func (r *PlanRepo) GeneratePlan(c *gin.Context, req request.GeneratePlanReq) *web.JsonResult {
	uid := jwtx.GetUid(c)

	// 检查用户是否已有活跃计划
	existingPlan, err := r.model.GetActiveLearningPlan(uid)
	if err != nil {
		logrus.WithError(err).Error("获取用户活跃计划失败 uid:", uid)
		return web.JsonInternalError(err)
	}

	// 如果用户已有活跃计划且不强制重新生成，则返回错误
	if existingPlan != nil && !req.ForceRegenerate {
		return web.JsonParamErr("用户已有活跃计划，如需重新生成请设置forceRegenerate=true")
	}

	// 检查是否已经有正在进行的计划生成
	if r.isGeneratingPlan(uid) {
		// 如果已经有正在进行的计划生成，返回状态信息
		return web.JsonData(map[string]any{
			"message": "计划生成正在进行中，请稍后查询",
			"status":  "processing",
		})
	}

	// 如果存在活跃计划，立即将其状态设置为无效
	if existingPlan != nil {
		// 使用事务将现有计划标记为无效
		err = r.model.Tx(func(txDb *dbx.DBExtension) error {
			return txDb.Model(&model.LearningPlan{}).Where(model.LearningPlan{Uid: uid, Status: 1}).Update("status", 0).Error
		})

		if err != nil {
			logrus.WithError(err).Error("使现有计划失效失败 uid:", uid)
			return web.JsonInternalError(err)
		}

		logrus.Infof("用户 %s 的现有计划已设置为无效", uid)
	}

	// 创建临时问卷对象用于AI服务调用和保存
	tempQuestionnaire := &model.UserQuestionnaire{
		Uid:               uid,
		CurrentLevel:      req.CurrentLevel,
		TargetLevel:       req.TargetLevel,
		DailyStudyMinutes: req.DailyStudyMinutes,
		MotivationSource:  req.MotivationSource,
		DesiredAbility:    req.DesiredAbility,
	}

	// 标记计划生成开始
	r.markPlanGenerationStarted(uid)

	// 获取目标语言代码，因为在goroutine中无法访问gin上下文
	// targetLangCode := jwtx.GetTargetLangCode(c)

	// 在后台goroutine中执行计划生成
	go func() {
		// 使用 singleflight 确保同一用户的并发请求只执行一次
		key := fmt.Sprintf("generate_plan_%s", uid)

		// 使用 singleflight 执行生成计划的逻辑
		result, err := generatePlanGroup.Do(key, func() (any, error) {
			logrus.Infof("开始为用户 %s 生成学习计划", uid)

			// 获取AI使用的资源
			// 首先获取与目标语言相关的资源关系
			var resourceRelations []*model.ResourceRelation
			if err := r.resourceModel.GetList(&resourceRelations, ""); err != nil {
				// if err := r.resourceModel.GetList(&resourceRelations, model.ResourceRelation{LangCode: targetLangCode}); err != nil {
				logrus.WithError(err).Error("获取资源关系失败 uid:", uid)
				return nil, err
			}

			// 提取资源ID
			var resourceIds []string
			for _, relation := range resourceRelations {
				resourceIds = append(resourceIds, relation.ResourceId)
			}

			// 如果没有找到资源关系，返回空列表
			if len(resourceIds) == 0 {
				return &types.PlanAIResponse{}, nil
			}

			// 获取资源
			resources, err := r.resourceModel.GetByIds(resourceIds)
			if err != nil {
				logrus.WithError(err).Error("获取资源失败 uid:", uid)
				return nil, err
			}

			// 创建一个新的上下文，因为原始的gin上下文可能已经关闭
			ctx := context.Background()

			// 过滤掉tags为空的资源
			validResources := util.FilterEmptyTagsResources(resources)

			// 先调用AI服务筛选资源
			logrus.Info("开始筛选资源...")
			// filteredResources, err := r.callFilterResourcesAIService(ctx, tempQuestionnaire, validResources)
			// if err != nil {
			// 	logrus.WithError(err).Error("筛选资源失败 uid:", uid)
			// 	return nil, err
			// }

			// 准备资源信息
			// resourceInfos := util.PrepareResourceInfos(filteredResources)
			resourceInfos := util.PrepareResourceInfos(validResources)

			// 调用分阶段生成计划的函数
			aiResponse, err := r.callGenerateStagedPlanAIService(ctx, tempQuestionnaire, resourceInfos)
			if err != nil {
				logrus.WithError(err).Error("生成分阶段计划失败 uid:", uid)
				return nil, err
			}

			return aiResponse, nil

		})

		// 无论成功还是失败，最后都要清理状态
		defer r.markPlanGenerationCompleted(uid)

		if err != nil {
			logrus.WithError(err).Error("生成计划失败 uid:", uid)
			return
		}

		// 类型断言，将 result 转换为 *types.PlanAIResponse
		aiResponse, ok := result.(*types.PlanAIResponse)
		if !ok {
			err := errors.New("类型断言失败，无法将结果转换为 PlanAIResponse")
			logrus.Error(err)
			return
		}
		// 打印AI响应内容
		jsonBytes, err := json.MarshalIndent(aiResponse, "", "  ")
		if err != nil {
			logrus.WithError(err).Error("序列化AI响应失败")
			return
		}
		logrus.Infof("AI生成的计划响应: %s", string(jsonBytes))
		if !ok {
			err := errors.New("类型断言失败，无法将结果转换为 PlanAIResponse")
			logrus.Error(err)
			return
		}

		// 计算结束时间
		var endDate time.Time

		// 如果有阶段，使用最后一个阶段的结束时间
		if len(aiResponse.Stages) > 0 {
			lastStage := aiResponse.Stages[len(aiResponse.Stages)-1]
			if lastStage.EndDate != "" {
				parsedEndDate, err := timex.ParseStandard(lastStage.EndDate)
				if err == nil {
					endDate = parsedEndDate
				}
			}
		}

		// 使用事务处理保存问卷和创建计划
		err = r.model.Tx(func(txDb *dbx.DBExtension) error {
			// 1. 获取用户问卷（如果存在）
			existingQuestionnaire, err := r.model.GetUserQuestionnaire(uid)
			if err != nil {
				return err
			}

			var questionnaireId string
			if existingQuestionnaire == nil {
				// 2. 创建新问卷
				newQuestionnaire := &model.UserQuestionnaire{
					Uid:               uid,
					MotivationSource:  tempQuestionnaire.MotivationSource,
					DesiredAbility:    tempQuestionnaire.DesiredAbility,
					CurrentLevel:      req.CurrentLevel,
					TargetLevel:       req.TargetLevel,
					DailyStudyMinutes: req.DailyStudyMinutes,
				}

				// 使用事务保存问卷
				if err := txDb.Create(newQuestionnaire).Error; err != nil {
					logrus.WithError(err).Error("保存用户问卷失败 uid:", uid)
					return err
				}
				questionnaireId = newQuestionnaire.Id
			} else {
				questionnaireId = existingQuestionnaire.Id
			}

			// 3.2 创建新计划
			now := timex.Now()
			plan := &model.LearningPlan{
				Uid:             uid,
				StartLevel:      req.CurrentLevel,
				TargetLevel:     req.TargetLevel,
				StartDate:       now,
				EndDate:         endDate,
				Status:          1,
				QuestionnaireId: questionnaireId,
			}

			if err := txDb.Create(plan).Error; err != nil {
				return err
			}

			// 3.3 创建计划阶段
			for i, stageResponse := range aiResponse.Stages {

				// 收集阶段的资源ID
				var resourceIds []string

				// 如果有周数据，从周数据中收集资源
				if len(stageResponse.Weeks) > 0 && len(stageResponse.Weeks[0].Resources) > 0 {
					for _, resourceResponse := range stageResponse.Weeks[0].Resources {
						resourceIds = append(resourceIds, resourceResponse.ResourceId)
					}
				}

				// 将资源ID列表转换为JSON字符串
				resourceIdsJSON, err := json.Marshal(resourceIds)
				if err != nil {
					logrus.WithError(err).Error("序列化资源ID列表失败")
					return err
				}

				stage := &model.LearningPlanStage{
					PlanId:      plan.Id,
					StageDesc:   stageResponse.StageDesc, // 使用AI返回的阶段描述
					Objective:   stageResponse.Objective,
					SortOrder:   i + 1,
					ResourceIds: string(resourceIdsJSON), // 保存资源ID列表
				}

				if err := txDb.Create(stage).Error; err != nil {
					return err
				}
			}

			return nil
		})

		if err != nil {
			logrus.WithError(err).Error("创建计划失败 uid:", uid)
			return
		}

		logrus.Infof("用户 %s 的学习计划生成完成", uid)
	}()

	// 立即返回，告知用户计划生成已开始
	message := "计划生成已开始，请稍后查询"
	if existingPlan != nil {
		message = "现有计划已失效，新计划生成已开始，请稍后查询"
	}

	return web.JsonData(map[string]any{
		"message": message,
		"status":  "processing",
	})
}

// GetPlan 获取用户当前活跃的学习计划
func (r *PlanRepo) GetPlan(c *gin.Context, req request.GetPlanReq) *web.JsonResult {
	uid := jwtx.GetUid(c)

	var plan *model.LearningPlan
	var err error

	// 1. 获取计划
	if req.PlanId != "" {
		// 如果提供了计划ID，则获取指定的计划
		plan = &model.LearningPlan{Model: model.Model{Id: req.PlanId}}
		found, err := r.model.GetOne(plan, *plan)
		if err != nil {
			logrus.WithError(err).Error("获取指定计划失败 uid:", uid)
			return web.JsonInternalError(err)
		}
		if !found {
			return web.JsonErrorCodeMsg(404, "未找到指定的计划")
		}
		// 验证计划是否属于当前用户
		if plan.Uid != uid {
			return web.JsonErrorCodeMsg(403, "无权访问该计划")
		}
	} else {
		// 否则获取用户当前活跃的计划
		plan, err = r.model.GetActiveLearningPlan(uid)
		if err != nil {
			logrus.WithError(err).Error("获取活跃计划失败 uid:", uid)
			return web.JsonInternalError(err)
		}

		if plan == nil {
			return web.JsonEmptyData()
		}
	}

	// 2. 获取计划阶段
	stages, err := r.model.GetPlanStages(plan.Id)
	if err != nil {
		logrus.WithError(err).Error("获取计划阶段失败 uid:", uid)
		return web.JsonInternalError(err)
	}
	// 3. 构建响应
	resp := &response.LearningPlanResp{
		Id:              plan.Id,
		StartLevel:      plan.StartLevel,
		TargetLevel:     plan.TargetLevel,
		StartDate:       timex.FormatStandard(plan.StartDate),
		EndDate:         timex.FormatStandard(plan.EndDate),
		Status:          plan.Status,
		StudyDaysOfWeek: &plan.StudyDaysOfWeek,
		DailySentences:  plan.DailySentences,
	}
	// 4. 收集所有资源ID
	allResourceIds := make(map[string]bool)
	for _, stage := range stages {
		// 解析阶段的资源ID列表
		var stageResourceIds []string
		if stage.ResourceIds != "" {
			if err := json.Unmarshal([]byte(stage.ResourceIds), &stageResourceIds); err != nil {
				logrus.WithError(err).Error("解析阶段资源ID列表失败 uid:", uid)
				return web.JsonInternalError(err)
			}
			for _, resourceId := range stageResourceIds {
				allResourceIds[resourceId] = true
			}
		}
	}

	// 5. 一次性获取所有资源信息
	var resourceMap map[string]*model.Resource
	var relationMap map[string]*model.ResourceRelation
	if len(allResourceIds) > 0 {
		resourceIds := make([]string, 0, len(allResourceIds))
		for resourceId := range allResourceIds {
			resourceIds = append(resourceIds, resourceId)
		}

		resources, relations, err := r.resourceModel.GetResourcesAndRelations(resourceIds, jwtx.GetTargetLangCode(c))
		if err != nil {
			logrus.WithError(err).Error("批量获取资源和关系失败 uid:", uid)
			return web.JsonInternalError(err)
		}

		// 创建映射
		resourceMap = make(map[string]*model.Resource)
		relationMap = make(map[string]*model.ResourceRelation)

		for _, resource := range resources {
			resourceMap[resource.Id] = resource
		}

		for _, relation := range relations {
			relationMap[relation.ResourceId] = relation
		}
	}

	// 6. 收集所有周ID
	allWeekIds := make([]string, 0)
	weekMap := make(map[string]*model.LearningPlanWeek)
	for _, stage := range stages {
		weeks, err := r.model.GetPlanWeeks(stage.Id)
		if err != nil {
			logrus.WithError(err).Error("获取阶段周数据失败 uid:", uid)
			return web.JsonInternalError(err)
		}
		for _, week := range weeks {
			allWeekIds = append(allWeekIds, week.Id)
			weekMap[week.Id] = week
		}
	}

	// 7. 批量获取所有天数据
	allDays, err := r.model.GetWeeksDays(allWeekIds)
	if err != nil {
		logrus.WithError(err).Error("批量获取天数据失败 uid:", uid)
		return web.JsonInternalError(err)
	}

	// 8. 按周ID分组天数据
	daysByWeek := make(map[string][]*model.LearningPlanDay)
	for _, day := range allDays {
		daysByWeek[day.WeekId] = append(daysByWeek[day.WeekId], day)
	}

	// 9. 添加阶段到响应
	for _, stage := range stages {
		stageResp := response.PlanStageResp{
			Id:        stage.Id,
			StageDesc: stage.StageDesc, // 添加阶段描述
			Objective: stage.Objective,
		}

		// 10. 获取阶段的周数据
		weeks, err := r.model.GetPlanWeeks(stage.Id)
		if err != nil {
			logrus.WithError(err).Error("获取阶段周数据失败 uid:", uid)
			return web.JsonInternalError(err)
		}

		// 11. 处理每个周
		for _, week := range weeks {
			weekResp := response.PlanWeekResp{
				WeekNumber: week.WeekNumber,
				Days:       []response.PlanDayResp{},
			}

			// 12. 从缓存中获取周的天数据
			days := daysByWeek[week.Id]

			// 13. 收集所有资源ID并构建天数据
			allResourceIds := make(map[string]bool)
			for _, day := range days {
				var dayResourceIds []string
				if day.ResourceIds != "" {
					if err := json.Unmarshal([]byte(day.ResourceIds), &dayResourceIds); err != nil {
						logrus.WithError(err).Error("解析日资源ID失败 uid:", uid)
						return web.JsonInternalError(err)
					}
					for _, resourceId := range dayResourceIds {
						allResourceIds[resourceId] = true
					}
				}

				// 构建天数据
				dayResp := response.PlanDayResp{
					Id:               day.Id,
					DayNumber:        day.DayNumber,
					Status:           day.Status,
					StudyDate:        timex.FormatStandard(day.StudyDate),
					CurrentSentences: day.CurrentSentences,
					TargetSentences:  day.TargetSentences,
					Resources:        []response.PlanDayResourceResp{},
				}

				// 添加资源详细信息
				for _, resourceId := range dayResourceIds {
					dayResourceResp := response.PlanDayResourceResp{
						ResourceId: resourceId,
					}

					if resource, ok := resourceMap[resourceId]; ok {
						dayResourceResp.ResourceCover = resource.Cover
						dayResourceResp.ResourceUrl = oss.GetOssSignedURL(r.client, resource.VideoURL)
					}

					if relation, ok := relationMap[resourceId]; ok {
						dayResourceResp.ResourceName = relation.Title
					}

					dayResp.Resources = append(dayResp.Resources, dayResourceResp)
				}

				weekResp.Days = append(weekResp.Days, dayResp)
			}

			stageResp.Weeks = append(stageResp.Weeks, weekResp)
		}

		resp.Stages = append(resp.Stages, stageResp)
	}

	// 计算当前应该学习的天
	resp.CurrentDayId = r.getCurrentStudyDay(plan, allDays)

	return web.JsonData(resp)
}

// GetPlanWithWeeksAndDays 获取包含周和日数据的完整学习计划
func (r *PlanRepo) GetPlanWithWeeksAndDays(c *gin.Context, req request.GetPlanReq) *web.JsonResult {
	uid := jwtx.GetUid(c)

	var plan *model.LearningPlan
	var err error

	// 1. 获取计划
	if req.PlanId != "" {
		// 如果提供了计划ID，则获取指定的计划
		plan = &model.LearningPlan{Model: model.Model{Id: req.PlanId}}
		found, err := r.model.GetOne(plan, *plan)
		if err != nil {
			logrus.WithError(err).Error("获取指定计划失败 uid:", uid)
			return web.JsonInternalError(err)
		}
		if !found {
			return web.JsonErrorCodeMsg(404, "未找到指定的计划")
		}
		// 验证计划是否属于当前用户
		if plan.Uid != uid {
			return web.JsonErrorCodeMsg(403, "无权访问该计划")
		}
	} else {
		// 否则获取用户当前活跃的计划
		plan, err = r.model.GetActiveLearningPlan(uid)
		if err != nil {
			logrus.WithError(err).Error("获取活跃计划失败 uid:", uid)
			return web.JsonInternalError(err)
		}

		if plan == nil {
			return web.JsonEmptyData()
		}
	}

	// 2. 获取计划阶段
	stages, err := r.model.GetPlanStages(plan.Id)
	if err != nil {
		logrus.WithError(err).Error("获取计划阶段失败 uid:", uid)
		return web.JsonInternalError(err)
	}

	// 3. 构建响应
	resp := &response.LearningPlanResp{
		Id:              plan.Id,
		StartLevel:      plan.StartLevel,
		TargetLevel:     plan.TargetLevel,
		StartDate:       timex.FormatStandard(plan.StartDate),
		EndDate:         timex.FormatStandard(plan.EndDate),
		Status:          plan.Status,
		StudyDaysOfWeek: &plan.StudyDaysOfWeek,
		DailySentences:  plan.DailySentences,
	}

	// 4. 收集所有资源ID
	allResourceIds := make(map[string]bool)
	for _, stage := range stages {
		// 解析阶段的资源ID列表
		var stageResourceIds []string
		if stage.ResourceIds != "" {
			if err := json.Unmarshal([]byte(stage.ResourceIds), &stageResourceIds); err != nil {
				logrus.WithError(err).Error("解析阶段资源ID列表失败 uid:", uid)
				return web.JsonInternalError(err)
			}
			for _, resourceId := range stageResourceIds {
				allResourceIds[resourceId] = true
			}
		}
	}

	// 5. 一次性获取所有资源信息
	var resourceMap map[string]*model.Resource
	var relationMap map[string]*model.ResourceRelation
	if len(allResourceIds) > 0 {
		resourceIds := make([]string, 0, len(allResourceIds))
		for resourceId := range allResourceIds {
			resourceIds = append(resourceIds, resourceId)
		}

		resources, relations, err := r.resourceModel.GetResourcesAndRelations(resourceIds, jwtx.GetTargetLangCode(c))
		if err != nil {
			logrus.WithError(err).Error("批量获取资源和关系失败 uid:", uid)
			return web.JsonInternalError(err)
		}

		// 创建映射
		resourceMap = make(map[string]*model.Resource)
		relationMap = make(map[string]*model.ResourceRelation)

		for _, resource := range resources {
			resourceMap[resource.Id] = resource
		}

		for _, relation := range relations {
			relationMap[relation.ResourceId] = relation
		}
	}

	// 6. 收集所有周ID
	allWeekIds := make([]string, 0)
	weekMap := make(map[string]*model.LearningPlanWeek)
	for _, stage := range stages {
		weeks, err := r.model.GetPlanWeeks(stage.Id)
		if err != nil {
			logrus.WithError(err).Error("获取阶段周数据失败 uid:", uid)
			return web.JsonInternalError(err)
		}
		for _, week := range weeks {
			allWeekIds = append(allWeekIds, week.Id)
			weekMap[week.Id] = week
		}
	}

	// 7. 批量获取所有天数据
	allDays, err := r.model.GetWeeksDays(allWeekIds)
	if err != nil {
		logrus.WithError(err).Error("批量获取天数据失败 uid:", uid)
		return web.JsonInternalError(err)
	}

	// 8. 按周ID分组天数据
	daysByWeek := make(map[string][]*model.LearningPlanDay)
	for _, day := range allDays {
		daysByWeek[day.WeekId] = append(daysByWeek[day.WeekId], day)
	}

	// 9. 处理每个阶段
	for _, stage := range stages {
		stageResp := response.PlanStageResp{
			Id:        stage.Id,
			StageDesc: stage.StageDesc,
			Objective: stage.Objective,
		}

		// 10. 获取阶段的周数据
		weeks, err := r.model.GetPlanWeeks(stage.Id)
		if err != nil {
			logrus.WithError(err).Error("获取阶段周数据失败 uid:", uid)
			return web.JsonInternalError(err)
		}

		// 11. 处理每个周
		for _, week := range weeks {
			weekResp := response.PlanWeekResp{
				WeekNumber: week.WeekNumber,
				Days:       []response.PlanDayResp{},
			}

			// 12. 从缓存中获取周的天数据
			days := daysByWeek[week.Id]

			// 13. 收集周的资源ID并构建天数据
			weekResourceIds := make(map[string]bool)
			for _, day := range days {
				var dayResourceIds []string
				if day.ResourceIds != "" {
					if err := json.Unmarshal([]byte(day.ResourceIds), &dayResourceIds); err != nil {
						logrus.WithError(err).Error("解析日资源ID失败 uid:", uid)
						return web.JsonInternalError(err)
					}
					for _, resourceId := range dayResourceIds {
						weekResourceIds[resourceId] = true
					}
				}

				// 构建天数据
				dayResp := response.PlanDayResp{
					Id:               day.Id,
					DayNumber:        day.DayNumber,
					Status:           day.Status,
					StudyDate:        timex.FormatStandard(day.StudyDate),
					CurrentSentences: day.CurrentSentences,
					TargetSentences:  day.TargetSentences,
					Resources:        []response.PlanDayResourceResp{},
				}

				// 添加资源详细信息
				for _, resourceId := range dayResourceIds {
					dayResourceResp := response.PlanDayResourceResp{
						ResourceId: resourceId,
					}

					if resource, ok := resourceMap[resourceId]; ok {
						dayResourceResp.ResourceCover = resource.Cover
						dayResourceResp.ResourceUrl = oss.GetOssSignedURL(r.client, resource.VideoURL)
					}

					if relation, ok := relationMap[resourceId]; ok {
						dayResourceResp.ResourceName = relation.Title
					}

					dayResp.Resources = append(dayResp.Resources, dayResourceResp)
				}

				weekResp.Days = append(weekResp.Days, dayResp)
			}

			stageResp.Weeks = append(stageResp.Weeks, weekResp)
		}

		resp.Stages = append(resp.Stages, stageResp)
	}

	// 计算当前应该学习的天
	resp.CurrentDayId = r.getCurrentStudyDay(plan, allDays)

	return web.JsonData(resp)
}

// InvalidatePlan 使用户的学习计划失效
func (r *PlanRepo) InvalidatePlan(c *gin.Context, req request.InvalidatePlanReq) *web.JsonResult {
	uid := jwtx.GetUid(c)

	if req.PlanId != "" {
		// 如果提供了计划ID，则只使该计划失效
		plan := &model.LearningPlan{Model: model.Model{Id: req.PlanId}}
		found, err := r.model.GetOne(plan, *plan)
		if err != nil {
			logrus.WithError(err).Error("获取指定计划失败")
			return web.JsonInternalError(err)
		}
		if !found {
			return web.JsonErrorCodeMsg(404, "未找到指定的计划")
		}
		// 验证计划是否属于当前用户
		if plan.Uid != uid {
			return web.JsonErrorCodeMsg(403, "无权操作该计划")
		}
		// 使计划失效
		plan.Status = 0
		if err := r.model.Update(plan, "id = ?", plan.Id); err != nil {
			logrus.WithError(err).Error("使计划失效失败 uid:", uid)
			return web.JsonInternalError(err)
		}
	} else {
		// 否则使用户所有活跃计划失效
		if err := r.model.InvalidateUserPlans(uid); err != nil {
			logrus.WithError(err).Error("使用户计划失效失败 uid:", uid)
			return web.JsonInternalError(err)
		}
	}

	return web.JsonOK()
}

// GenerateFixedPlan 生成固定的学习计划
func (r *PlanRepo) GenerateFixedPlan(c *gin.Context, req request.GenerateFixedPlanReq) *web.JsonResult {
	uid := jwtx.GetUid(c)
	targetLangCode := jwtx.GetTargetLangCode(c)

	// 检查用户是否已有活跃计划
	existingPlan, err := r.model.GetActiveLearningPlan(uid)
	if err != nil {
		logrus.WithError(err).Error("获取用户活跃计划失败 uid:", uid)
		return web.JsonInternalError(err)
	}

	// 如果用户已有活跃计划且不强制重新生成，则返回错误
	if existingPlan != nil && !req.ForceRegenerate {
		return web.JsonParamErr("用户已有活跃计划，如需重新生成请设置forceRegenerate=true")
	}

	// 验证学习日期配置
	if len(req.StudyDaysOfWeek) == 0 {
		return web.JsonParamErr("请配置每周学习的具体日期")
	}

	// 验证学习日期是否在1-7范围内
	for _, day := range req.StudyDaysOfWeek {
		if day < 1 || day > 7 {
			return web.JsonParamErr("学习日期必须在1-7之间（1代表周一，7代表周日）")
		}
	}

	// 验证每天学习句数
	if req.DailySentences <= 0 {
		return web.JsonParamErr("每天学习句数必须大于0")
	}

	// 获取第一个精选资源或第一个普通资源
	resource, err := r.model.GetFirstFeaturedResource(targetLangCode)
	if err != nil {
		logrus.WithError(err).Error("获取资源失败 uid:", uid)
		return web.JsonInternalError(err)
	}

	// 如果存在活跃计划，立即将其状态设置为无效
	if existingPlan != nil {
		err = r.model.Tx(func(txDb *dbx.DBExtension) error {
			return txDb.Model(&model.LearningPlan{}).Where(model.LearningPlan{Uid: uid, Status: 1}).Update("status", 0).Error
		})

		if err != nil {
			logrus.WithError(err).Error("使现有计划失效失败 uid:", uid)
			return web.JsonInternalError(err)
		}

		logrus.Infof("用户 %s 的现有计划已设置为无效", uid)
	}

	// 计算学习天数和周数
	totalLearnDays := 7                                                                      // 固定28天
	totalWeeks := (totalLearnDays + len(req.StudyDaysOfWeek) - 1) / len(req.StudyDaysOfWeek) // 向上取整

	// 使用事务处理创建计划
	err = r.model.Tx(func(txDb *dbx.DBExtension) error {
		// 1. 创建新计划
		now := timex.Now()
		endDate := now.AddDate(0, 0, totalLearnDays)

		plan := &model.LearningPlan{
			Uid:             uid,
			StartLevel:      req.CurrentLevel,
			TargetLevel:     req.TargetLevel,
			StartDate:       now,
			EndDate:         endDate,
			Status:          1,
			StudyDaysOfWeek: req.StudyDaysOfWeek,
			DailySentences:  req.DailySentences,
		}

		if err := txDb.Create(plan).Error; err != nil {
			return err
		}

		// 2. 创建阶段
		// 将资源ID序列化为JSON
		resourceIdsJSON, err := json.Marshal([]string{resource.Id})
		if err != nil {
			return err
		}

		stage := &model.LearningPlanStage{
			PlanId:      plan.Id,
			StageDesc:   "固定学习计划",
			Objective:   "通过固定学习计划提升英语水平",
			SortOrder:   1,
			ResourceIds: string(resourceIdsJSON),
		}

		if err := txDb.Create(stage).Error; err != nil {
			return err
		}

		// 3. 创建周计划
		currentDate := now
		for weekNum := 1; weekNum <= totalWeeks; weekNum++ {
			weekStartDate := currentDate
			weekEndDate := currentDate.AddDate(0, 0, 6)

			week := &model.LearningPlanWeek{
				StageId:    stage.Id,
				WeekNumber: weekNum,
				StartDate:  weekStartDate,
				EndDate:    weekEndDate,
			}

			if err := txDb.Create(week).Error; err != nil {
				return err
			}

			// 4. 为每周创建学习天的计划
			// 根据用户配置的学习日期创建天计划
			for _, dayOfWeek := range req.StudyDaysOfWeek {
				// 计算实际学习日期
				studyDate := calculateStudyDate(weekStartDate, dayOfWeek)

				// 将资源ID序列化为JSON
				resourceIdsJSON, err := json.Marshal([]string{resource.Id})
				if err != nil {
					return err
				}

				day := &model.LearningPlanDay{
					WeekId:           week.Id,
					DayNumber:        dayOfWeek,          // 使用实际的学习日期（1-7，代表周一到周日）
					StudyDate:        studyDate,          // 实际学习日期
					Status:           0,                  // 初始状态：未开始
					CurrentSentences: 0,                  // 初始已学习句数
					TargetSentences:  req.DailySentences, // 目标学习句数
					ResourceIds:      string(resourceIdsJSON),
				}

				if err := txDb.Create(day).Error; err != nil {
					return err
				}
			}

			// 移动到下一周的开始
			currentDate = currentDate.AddDate(0, 0, 7)
		}

		return nil
	})

	if err != nil {
		logrus.WithError(err).Error("创建固定计划失败 uid:", uid)
		return web.JsonInternalError(err)
	}

	logrus.Infof("用户 %s 的固定学习计划生成完成，总天数: %d，总周数: %d", uid, totalLearnDays, totalWeeks)

	return web.JsonData(map[string]any{
		"message":        fmt.Sprintf("固定学习计划生成完成，总天数: %d，总周数: %d", totalLearnDays, totalWeeks),
		"status":         "completed",
		"totalLearnDays": totalLearnDays,
		"totalWeeks":     totalWeeks,
	})
}

// 计算每周的学习时长（秒）
func calculateWeeklyStudyTime(dailyStudyMinutes int) int {
	return dailyStudyMinutes * 60 * 7 // 每天学习分钟数 * 60秒 * 7天
}

// generateStageResources 生成阶段资源排序
func (r *PlanRepo) generateStageResources(ctx context.Context, questionnaire *model.UserQuestionnaire, resourceInfos []map[string]any) (*types.StageResourceListResponse, error) {
	// 生成阶段时长映射
	stageDurations := util.GenerateStageDurations(questionnaire.CurrentLevel, questionnaire.TargetLevel)

	// 构建阶段资源排序的提示信息
	prompt := util.BuildStageResourcesPrompt(questionnaire, resourceInfos, stageDurations)
	logrus.Infof("阶段资源排序 AI Prompt: %s", prompt)

	// 从环境变量或配置中获取API密钥
	apiKey := os.Getenv("ARK_API_KEY")

	// 调用AI服务
	content, err := util.CallAIService(ctx, prompt, apiKey, "deepseek-v3-250324")
	if err != nil {
		logrus.WithError(err).Error("调用AI服务生成阶段资源排序失败")
		return nil, err
	}

	// 预处理内容，提取JSON
	jsonContent, err := util.ExtractJSONFromContent(content)
	if err != nil {
		logrus.WithError(err).Error("提取JSON内容失败")
		return nil, err
	}

	// 解析AI返回的JSON字符串为StageResourcesSortResponse结构
	var sortResponse types.StageResourcesSortResponse
	if err := json.Unmarshal([]byte(jsonContent), &sortResponse); err != nil {
		logrus.WithError(err).Error("解析AI生成的阶段资源排序失败")
		return nil, err
	}

	// 验证响应数据的有效性
	if len(sortResponse.Stages) == 0 {
		logrus.Error("AI生成的阶段资源排序为空")
		return nil, errors.New("AI生成的阶段资源排序为空")
	}

	// 创建资源ID到序号的映射
	resourceIdToNumber := make(map[string]int)
	for i, info := range resourceInfos {
		if id, ok := info["id"].(string); ok {
			resourceIdToNumber[id] = i + 1 // 从1开始的序号
		}
	}

	// 创建序号到资源ID的映射
	numberToResourceId := make(map[int]string)
	for id, number := range resourceIdToNumber {
		numberToResourceId[number] = id
	}

	// 转换为StageResourceListResponse格式
	response := &types.StageResourceListResponse{
		Stages: make([]types.StageResourceList, len(sortResponse.Stages)),
	}

	for i, stage := range sortResponse.Stages {
		// 创建阶段响应
		stageResponse := types.StageResourceList{
			BaseStageResponse: stage.BaseStageResponse,
			ResourceIds:       make([]string, 0, len(stage.ResourceNumbers)),
		}

		// 将资源序号转换为资源ID
		for _, number := range stage.ResourceNumbers {
			if resourceId, ok := numberToResourceId[number]; ok {
				stageResponse.ResourceIds = append(stageResponse.ResourceIds, resourceId)
			}
		}

		response.Stages[i] = stageResponse
	}

	return response, nil
}

// callGenerateStagedPlanAIService 调用AI服务分阶段生成计划
func (r *PlanRepo) callGenerateStagedPlanAIService(ctx context.Context, questionnaire *model.UserQuestionnaire, resourceInfos []map[string]any) (*types.PlanAIResponse, error) {
	// 1. 首先调用AI服务生成阶段资源列表
	logrus.Info("开始生成阶段资源列表...")
	stageResources, err := r.generateStageResources(ctx, questionnaire, resourceInfos)
	if err != nil {
		logrus.WithError(err).Error("生成阶段资源列表失败")
		return nil, err
	}

	// 2. 创建资源使用状态映射
	resourceUsageMap := make(map[string]*types.ResourceUsage)
	for _, info := range resourceInfos {
		if id, ok := info["id"].(string); ok {
			if duration, ok := info["duration"].(int); ok {
				resourceUsageMap[id] = &types.ResourceUsage{
					ResourceId: id,
					Duration:   duration,
				}
			}
		}
	}

	// 3. 计算每周学习时长
	weeklyStudyTime := calculateWeeklyStudyTime(questionnaire.DailyStudyMinutes)
	logrus.Infof("每周学习时长: %d秒", weeklyStudyTime)

	// 生成阶段时长映射
	stageDurations := util.GenerateStageDurations(questionnaire.CurrentLevel, questionnaire.TargetLevel)

	// 4. 生成阶段计划
	finalResponse := &types.PlanAIResponse{
		Stages: make([]types.PlanStageAIResponse, len(stageResources.Stages)),
	}

	// 5. 处理每个阶段
	for i, stage := range stageResources.Stages {
		// 创建阶段响应
		stageResponse := types.PlanStageAIResponse{
			BaseStageResponse: stage.BaseStageResponse,
			Weeks:             make([]types.PlanWeekAIResponse, 0),
		}

		// 获取当前阶段的时长（秒）
		stageDurationStr, ok := stageDurations[stage.StageDesc]
		if !ok {
			logrus.Warnf("未找到阶段 %s 的时长信息，使用默认值", stage.StageDesc)
			stageDurationStr = "60-90h" // 使用默认值
		}

		// 将时长字符串转换为秒数
		minSeconds, maxSeconds := util.ConvertDurationToSeconds(stageDurationStr)
		// 使用平均时长作为阶段时长
		stageDuration := (minSeconds + maxSeconds) / 2
		logrus.Infof("阶段 %s 目标时长: %d-%d秒 (平均: %d秒)", stage.StageDesc, minSeconds, maxSeconds, stageDuration)

		// 计算阶段需要的总周数（向上取整）
		totalWeeks := (stageDuration + weeklyStudyTime - 1) / weeklyStudyTime
		if totalWeeks < 1 {
			totalWeeks = 1 // 确保至少有一周
		}
		logrus.Infof("阶段 %s 需要 %d 周完成", stage.StageDesc, totalWeeks)

		// 计算每周应该完成的学习时长
		weeklyTargetDuration := stageDuration / totalWeeks
		if weeklyTargetDuration < 1 {
			weeklyTargetDuration = 1 // 确保每周至少有一个单位的学习时长
		}

		// 初始化所有周
		for weekNum := 1; weekNum <= totalWeeks; weekNum++ {
			stageResponse.Weeks = append(stageResponse.Weeks, types.PlanWeekAIResponse{
				WeekNumber: weekNum,
				Resources:  make([]types.PlanResourceAIResponse, 0),
			})
		}

		// 计算每个资源应该分配到的周数
		resourceCount := len(stage.ResourceIds)
		resourcesPerWeek := (resourceCount + totalWeeks - 1) / totalWeeks // 向上取整

		// 当前处理的周索引和资源计数
		currentWeekIndex := 0
		currentWeekResourceCount := 0
		currentWeekDuration := 0

		// 处理每个资源
		for _, resourceId := range stage.ResourceIds {
			// 获取资源信息
			resourceUsage, ok := resourceUsageMap[resourceId]
			if !ok {
				continue
			}

			// 如果当前周的资源数量达到目标，或者当前周时长接近目标，移动到下一周
			if (currentWeekResourceCount >= resourcesPerWeek ||
				currentWeekDuration >= weeklyTargetDuration) &&
				currentWeekIndex < totalWeeks-1 {
				currentWeekIndex++
				currentWeekResourceCount = 0
				currentWeekDuration = 0
			}

			// 计算在当前周可以学习的次数
			remainingWeekTime := weeklyStudyTime - currentWeekDuration
			lsCount := 1 // 默认至少学习一次

			if resourceUsage.Duration > 0 {
				// 计算在当前周剩余时间内可以学习的次数
				possibleCount := remainingWeekTime / resourceUsage.Duration
				if possibleCount > 0 {
					lsCount = possibleCount
				}
			}

			// 如果当前周剩余时间不足，且还有下一周，则减少学习次数
			if currentWeekDuration+(resourceUsage.Duration*lsCount) > weeklyStudyTime && currentWeekIndex < totalWeeks-1 {
				lsCount = (weeklyStudyTime - currentWeekDuration) / resourceUsage.Duration
				if lsCount < 1 {
					lsCount = 1
				}
			}

			// 添加资源到当前周
			stageResponse.Weeks[currentWeekIndex].Resources = append(
				stageResponse.Weeks[currentWeekIndex].Resources,
				types.PlanResourceAIResponse{
					ResourceId: resourceId,
					LsCount:    lsCount,
				},
			)

			// 更新当前周已用时间和资源计数
			currentWeekDuration += resourceUsage.Duration * lsCount
			currentWeekResourceCount++
		}

		// 移除空的周
		validWeeks := make([]types.PlanWeekAIResponse, 0, len(stageResponse.Weeks))
		for _, week := range stageResponse.Weeks {
			if len(week.Resources) > 0 {
				validWeeks = append(validWeeks, week)
			}
		}
		stageResponse.Weeks = validWeeks

		// 如果没有分配任何资源到周，添加一个空的第一周
		if len(stageResponse.Weeks) == 0 {
			stageResponse.Weeks = append(stageResponse.Weeks, types.PlanWeekAIResponse{
				WeekNumber: 1,
				Resources:  make([]types.PlanResourceAIResponse, 0),
			})
		}

		finalResponse.Stages[i] = stageResponse
	}

	// 6. 计算每个阶段的开始和结束时间
	finalResponse.Stages = util.CalculatePlanStagesTime(finalResponse.Stages)

	return finalResponse, nil
}

// callFilterResourcesAIService 调用AI服务筛选资源
func (r *PlanRepo) callFilterResourcesAIService(ctx context.Context, questionnaire *model.UserQuestionnaire, resources []*model.Resource) ([]*model.Resource, error) {
	// 获取配置的每批最大资源数量
	maxResourcesPerBatch := r.config.AI.MaxResourcesPerBatch
	if maxResourcesPerBatch <= 0 {
		// 如果配置无效，使用默认值50
		maxResourcesPerBatch = 50
	}

	totalResources := len(resources)
	logrus.Infof("开始筛选资源，总资源数量: %d，每批处理数量: %d", totalResources, maxResourcesPerBatch)

	// 如果资源数量小于等于每批最大数量，直接处理
	if totalResources <= maxResourcesPerBatch {
		return r.filterResourcesBatch(ctx, questionnaire, resources)
	}

	// 计算需要处理的批次数
	batchCount := (totalResources + maxResourcesPerBatch - 1) / maxResourcesPerBatch
	logrus.Infof("将分成 %d 个批次并发处理", batchCount)

	// 创建通道用于接收每个批次的处理结果
	type batchResult struct {
		resources []*model.Resource
		err       error
		batchNum  int
	}
	resultChan := make(chan batchResult, batchCount)

	// 创建一个WaitGroup来等待所有goroutine完成
	var wg sync.WaitGroup

	// 并发处理每个批次
	for i := 0; i < batchCount; i++ {
		wg.Add(1)
		go func(batchIndex int) {
			defer wg.Done()

			// 计算当前批次的起始和结束索引
			start := batchIndex * maxResourcesPerBatch
			end := min((batchIndex+1)*maxResourcesPerBatch, totalResources)

			// 获取当前批次的资源
			batchResources := resources[start:end]
			logrus.Infof("开始处理第 %d/%d 批资源，数量: %d", batchIndex+1, batchCount, len(batchResources))

			// 处理当前批次
			filteredResources, err := r.filterResourcesBatch(ctx, questionnaire, batchResources)

			// 将结果发送到通道
			resultChan <- batchResult{
				resources: filteredResources,
				err:       err,
				batchNum:  batchIndex + 1,
			}

			if err != nil {
				logrus.WithError(err).Errorf("处理第 %d 批资源失败", batchIndex+1)
			} else {
				logrus.Infof("第 %d 批资源处理完成，筛选出 %d 个资源", batchIndex+1, len(filteredResources))
			}
		}(i)
	}

	// 启动一个goroutine来关闭结果通道
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// 收集所有批次的结果
	var allFilteredResources []*model.Resource
	var allResourceIds []string
	successCount := 0

	// 使用互斥锁保护共享资源
	var mu sync.Mutex

	for result := range resultChan {
		if result.err != nil {
			// 如果处理失败，记录错误但继续处理其他批次的结果
			continue
		}

		mu.Lock()
		successCount++
		// 收集当前批次筛选出的资源
		for _, resource := range result.resources {
			// 避免重复添加
			if !contains(allResourceIds, resource.Id) {
				allResourceIds = append(allResourceIds, resource.Id)
				allFilteredResources = append(allFilteredResources, resource)
			}
		}
		mu.Unlock()
	}

	// 如果所有批次都处理失败，返回错误
	if successCount == 0 {
		logrus.Error("所有批次处理均失败")
		return nil, errors.New("所有批次处理均失败")
	}

	// 如果所有批次处理后没有筛选出任何资源，返回错误
	if len(allFilteredResources) == 0 {
		logrus.Error("所有批次处理后，AI筛选的资源为空")
		return nil, errors.New("AI筛选的资源为空")
	}

	// 记录成功的响应
	logrus.Infof("所有批次处理完成，成功处理 %d/%d 批次，筛选出 %d 个资源",
		successCount, batchCount, len(allFilteredResources))

	return allFilteredResources, nil
}

// filterResourcesBatch 处理单批资源
func (r *PlanRepo) filterResourcesBatch(ctx context.Context, questionnaire *model.UserQuestionnaire, resources []*model.Resource) ([]*model.Resource, error) {
	// 准备资源信息，包括ID和标签
	resourceInfos := util.PrepareResourceInfos(resources)

	// 构建筛选资源的提示信息
	prompt := util.BuildFilterResourcesPrompt(questionnaire, resourceInfos)
	logrus.Infof("筛选资源批次 AI Prompt: %s", prompt)

	// 从环境变量或配置中获取API密钥
	apiKey := os.Getenv("ARK_API_KEY")

	// 调用AI服务
	content, err := util.CallAIService(ctx, prompt, apiKey, "deepseek-v3-250324")
	if err != nil {
		logrus.WithError(err).Error("调用AI服务筛选资源失败")
		return nil, err
	}

	// 解析AI返回的JSON字符串为资源ID列表
	type FilteredResourcesResponse struct {
		ResourceIds []string `json:"resourceIds"`
	}

	var filteredResourcesResponse FilteredResourcesResponse
	if err := json.Unmarshal([]byte(content), &filteredResourcesResponse); err != nil {
		logrus.WithError(err).Error("解析AI筛选的资源响应失败")
		return nil, err
	}

	// 验证响应数据的有效性
	if len(filteredResourcesResponse.ResourceIds) == 0 {
		logrus.Error("AI筛选的资源ID为空")
		return nil, errors.New("AI筛选的资源ID为空")
	}

	// 根据筛选后的资源ID获取资源
	var filteredResources []*model.Resource
	resourceMap := make(map[string]*model.Resource)

	// 创建资源ID到资源的映射
	for _, resource := range resources {
		resourceMap[resource.Id] = resource
	}

	// 根据筛选后的资源ID获取资源
	for _, resourceId := range filteredResourcesResponse.ResourceIds {
		if resource, ok := resourceMap[resourceId]; ok {
			filteredResources = append(filteredResources, resource)
		}
	}

	// 记录成功的响应
	logrus.Infof("批次筛选后的资源数量: %d", len(filteredResources))

	return filteredResources, nil
}

// contains 检查字符串切片是否包含指定字符串
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// calculateStudyDate 根据周开始日期和学习日计算实际学习日期
func calculateStudyDate(weekStartDate time.Time, dayOfWeek int) time.Time {
	// 计算从周开始日期到目标学习日的天数
	// dayOfWeek: 1=周一, 2=周二, ..., 7=周日
	daysToAdd := dayOfWeek - 1 // 如果dayOfWeek=1(周一)，则不需要加天数

	// 从周开始日期开始计算
	return weekStartDate.AddDate(0, 0, daysToAdd)
}

// getCurrentStudyDay 获取当前应该学习的天ID
func (r *PlanRepo) getCurrentStudyDay(_ *model.LearningPlan, allDays []*model.LearningPlanDay) string {
	if len(allDays) == 0 {
		return ""
	}

	now := timex.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	// 按学习日期排序所有天
	sort.Slice(allDays, func(i, j int) bool {
		return allDays[i].StudyDate.Before(allDays[j].StudyDate)
	})

	// 找到今天或下一个学习日
	var currentDay *model.LearningPlanDay
	for _, day := range allDays {
		studyDate := time.Date(day.StudyDate.Year(), day.StudyDate.Month(), day.StudyDate.Day(), 0, 0, 0, 0, day.StudyDate.Location())

		// 如果学习日期是今天或未来，且状态不是已完成
		if (studyDate.Equal(today) || studyDate.After(today)) && day.Status != 2 {
			currentDay = day
			break
		}
	}

	// 如果没有找到当前天，返回第一个未完成的天
	if currentDay == nil {
		for _, day := range allDays {
			if day.Status != 2 {
				currentDay = day
				break
			}
		}
	}

	if currentDay == nil {
		return ""
	}

	return currentDay.Id
}

// UpdateSentences 更新学习句数
func (r *PlanRepo) UpdateSentences(c *gin.Context, req request.UpdateSentencesReq) *web.JsonResult {
	uid := jwtx.GetUid(c)

	// 解析日期
	var studyDate time.Time
	if req.Date != "" {
		parsedDate, err := time.Parse("2006-01-02", req.Date)
		if err != nil {
			return web.JsonParamErr("日期格式错误，请使用YYYY-MM-DD格式")
		}
		studyDate = parsedDate
	} else {
		// 如果没有提供日期，使用当前日期
		studyDate = time.Now()
	}

	// 验证句数
	if req.Sentences < 0 {
		return web.JsonParamErr("学习句数不能为负数")
	}

	// 更新学习句数
	err := r.model.UpdateDaySentencesByDate(uid, studyDate, req.Sentences)
	if err != nil {
		logrus.WithError(err).Error("更新学习句数失败 uid:", uid)
		return web.JsonInternalError(err)
	}

	return web.JsonOK()
}

// UpdateStudyDays 修改学习计划的学习日期配置
func (r *PlanRepo) UpdateStudyDays(c *gin.Context, req request.UpdateStudyDaysReq) *web.JsonResult {
	uid := jwtx.GetUid(c)

	// 验证学习日期配置
	if len(req.StudyDaysOfWeek) == 0 {
		return web.JsonParamErr("请配置每周学习的具体日期")
	}

	// 验证学习日期是否在1-7范围内
	for _, day := range req.StudyDaysOfWeek {
		if day < 1 || day > 7 {
			return web.JsonParamErr("学习日期必须在1-7之间（1代表周一，7代表周日）")
		}
	}

	// 获取用户活跃计划
	plan, err := r.model.GetActiveLearningPlan(uid)
	if err != nil {
		logrus.WithError(err).Error("获取活跃计划失败 uid:", uid)
		return web.JsonInternalError(err)
	}
	if plan == nil {
		return web.JsonEntityNotFound(c)
	}

	// 如果配置没有变化，直接返回成功
	if len(plan.StudyDaysOfWeek) == len(req.StudyDaysOfWeek) {
		same := true
		for i, day := range req.StudyDaysOfWeek {
			if i >= len(plan.StudyDaysOfWeek) || plan.StudyDaysOfWeek[i] != day {
				same = false
				break
			}
		}
		if same {
			return web.JsonOK()
		}
	}

	// 调用模型层方法更新学习日期配置
	err = r.model.UpdateLearningPlanStudyDays(plan.Id, req.StudyDaysOfWeek)
	if err != nil {
		logrus.WithError(err).Error("修改学习日期失败 uid:", uid)
		return web.JsonInternalError(err)
	}

	return web.JsonOK()
}
